import React, { useState, useEffect } from 'react';
import { Container, Typography, Alert, Snackbar, Box, Paper, Divider, TextField } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { machines, saveAllData, checkUnsavedChanges, discardChanges } from '../services/navbarApi';

const Machines = () => {
  const [machinesData, setMachinesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const columns = [
    { field: 'nomMachine', headerName: 'Nom Machine', type: 'string' },
    { field: 'poste', headerName: 'Poste', type: 'string' },
    { field: 'capacite', headerName: 'Capacité', type: 'number' },
    { field: 'tempsSetup', headerName: 'Temps Setup (min)', type: 'number' },
    { field: 'cadence', headerName: 'Cadence', type: 'number' },
    { field: 'disponibilite', headerName: 'Disponibilité', type: 'number' },
  ];

  useEffect(() => {
    fetchMachines(false); // Use cached data if available for faster loading
  }, []);

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (checkUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const fetchMachines = async (forceRefresh = false) => {
    try {
      setLoading(true);
      const data = await machines.getAll(forceRefresh); // Only force refresh when explicitly requested
      setMachinesData(data);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des machines');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.capacite = parseInt(newItem.capacite);
      newItem.tempsSetup = parseInt(newItem.tempsSetup);
      newItem.cadence = parseInt(newItem.cadence);
      newItem.disponibilite = parseInt(newItem.disponibilite);

      const addedItem = await machines.add(newItem);

      // Refresh data from the API to ensure we have the latest state
      const updatedData = await machines.getAll();
      setMachinesData(updatedData);

      showNotification('Machine ajoutée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout de la machine', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.capacite = parseInt(updatedItem.capacite);
      updatedItem.tempsSetup = parseInt(updatedItem.tempsSetup);
      updatedItem.cadence = parseInt(updatedItem.cadence);
      updatedItem.disponibilite = parseInt(updatedItem.disponibilite);

      const editedItem = await machines.update(updatedItem);
      setMachinesData(machinesData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Machine mise à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour de la machine', 'error');
    }
  };

  const handleDelete = async (id) => {
    try {
      await machines.delete(id);
      setMachinesData(machinesData.filter(item => item.id !== id));
      showNotification('Machine supprimée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression de la machine', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Handle navigation with unsaved changes
  const handleNavigation = (path) => {
    if (checkUnsavedChanges()) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      window.location.href = path;
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    discardChanges();
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Machines
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Search input field */}
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-start' }}>
          <TextField
            placeholder="Rechercher par Nom Machine..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: 'rgba(99, 102, 241, 0.6)', mr: 1 }} />,
            }}
            sx={{
              width: { xs: '100%', sm: '300px' },
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(15, 23, 42, 0.8)',
                borderRadius: '8px',
                '& fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.8)',
                },
              },
              '& .MuiInputBase-input': {
                color: 'white',
                '&::placeholder': {
                  color: 'rgba(255, 255, 255, 0.6)',
                  opacity: 1,
                },
              },
            }}
          />
        </Box>

        <DataTable
          title=""
          columns={columns}
          data={machinesData.filter(item => {
            if (!searchTerm.trim()) return true;
            const searchLower = searchTerm.toLowerCase();
            return item.nomMachine && item.nomMachine.toString().toLowerCase().includes(searchLower);
          })}
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSave={handleSave}
          loading={loading}
        />
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default Machines;
