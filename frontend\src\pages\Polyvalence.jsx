import { useState, useEffect } from 'react';
import { Container, Alert, Snackbar, Box, Paper, Divider, Typography, TextField } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { polyvalence, ressourcesHumaines, saveAllData, checkUnsavedChanges, discardChanges } from '../services/navbarApi';

const Polyvalence = () => {
  const [polyvalenceData, setPolyvalenceData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [operatorNames, setOperatorNames] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch operator names from Ressources Humaines
  const fetchOperatorNames = async () => {
    try {
      const ressourcesData = await ressourcesHumaines.getAll();
      const names = ressourcesData.map(resource => resource.nom).filter(name => name && name.trim() !== '');
      setOperatorNames(names);
    } catch (error) {
      console.error('Error fetching operator names:', error);
    }
  };

  const columns = [
    {
      field: 'operateur',
      headerName: 'Opérateur',
      type: 'select',
      options: operatorNames
    },

    { field: 'machineBA600', headerName: 'Machine BA 600', type: 'number' },
    { field: 'machineGraissage3', headerName: 'Machine graissage 3', type: 'number' },
    { field: 'machineSoudage2', headerName: 'Machine soudage 2', type: 'number' },
    { field: 'machineMarquage2', headerName: 'Machine marquage 2', type: 'number' },
    { field: 'machineEmballage2', headerName: 'Machine emballage 2', type: 'number' },
    { field: 'machineBA500', headerName: 'Machine BA 500', type: 'number' },
    { field: 'machineGraissage', headerName: 'Machine graissage', type: 'number' },
    { field: 'machineSoudage1', headerName: 'Machine soudage 1', type: 'number' },
    { field: 'machineMarquage1', headerName: 'Machine marquage 1', type: 'number' },
    { field: 'machineEmballage1', headerName: 'Machine emballage 1', type: 'number' },
  ];

  useEffect(() => {
    fetchOperatorNames();
    fetchPolyvalence(false); // Use cached data if available for faster loading
  }, []);

  // Sync Polyvalence data with Ressources Humaines changes
  const syncWithRessourcesHumaines = async (forceRefresh = false) => {
    try {
      const ressourcesData = await ressourcesHumaines.getAll(forceRefresh);
      const currentNames = ressourcesData.map(resource => resource.nom).filter(name => name && name.trim() !== '');

      // Update operator names for dropdown
      setOperatorNames(currentNames);

      // Get current polyvalence data
      const currentPolyvalence = await polyvalence.getAll(forceRefresh);

      // Check if any operators in Polyvalence no longer exist in Ressources Humaines
      // Remove entries for operators that no longer exist
      const updatedPolyvalence = currentPolyvalence.filter(poly =>
        currentNames.includes(poly.operateur)
      );

      // Remove deleted operators from backend
      const deletedOperators = currentPolyvalence.filter(poly =>
        !currentNames.includes(poly.operateur)
      );
      for (const deletedPoly of deletedOperators) {
        await polyvalence.delete(deletedPoly.id);
      }

      // Add new operators from Ressources Humaines that don't exist in Polyvalence
      const existingOperators = currentPolyvalence.map(poly => poly.operateur);
      const newOperators = currentNames.filter(name => !existingOperators.includes(name));

      for (const newOperator of newOperators) {
        const newPolyvalenceEntry = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          operateur: newOperator,
          machineBA600: 0,
          machineGraissage3: 0,
          machineSoudage2: 0,
          machineMarquage2: 0,
          machineEmballage2: 0,
          machineBA500: 0,
          machineGraissage: 0,
          machineSoudage1: 0,
          machineMarquage1: 0,
          machineEmballage1: 0,
        };
        updatedPolyvalence.push(newPolyvalenceEntry);
        await polyvalence.add(newPolyvalenceEntry);
      }

      // Update the state with synchronized data
      setPolyvalenceData(updatedPolyvalence);

    } catch (error) {
      // Error syncing with Ressources Humaines
    }
  };

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (checkUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Add focus event listener to refresh data when page becomes active
  useEffect(() => {
    const handleFocus = () => {
      // Refresh operator names when page becomes active (user navigates back)
      fetchOperatorNames();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  const fetchPolyvalence = async (forceRefresh = false) => {
    try {
      setLoading(true);
      // First sync with Ressources Humaines to ensure data consistency
      await syncWithRessourcesHumaines(forceRefresh);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des données de polyvalence');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.machineBA600 = parseInt(newItem.machineBA600);
      newItem.machineGraissage3 = parseInt(newItem.machineGraissage3);
      newItem.machineSoudage2 = parseInt(newItem.machineSoudage2);
      newItem.machineMarquage2 = parseInt(newItem.machineMarquage2);
      newItem.machineEmballage2 = parseInt(newItem.machineEmballage2);
      newItem.machineBA500 = parseInt(newItem.machineBA500);
      newItem.machineGraissage = parseInt(newItem.machineGraissage);
      newItem.machineSoudage1 = parseInt(newItem.machineSoudage1);
      newItem.machineMarquage1 = parseInt(newItem.machineMarquage1);
      newItem.machineEmballage1 = parseInt(newItem.machineEmballage1);

      const addedItem = await polyvalence.add(newItem);
      // Refresh data from the API to ensure we have the latest state
      const updatedData = await polyvalence.getAll();
      setPolyvalenceData(updatedData);
      showNotification('Polyvalence ajoutée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout de la polyvalence', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.machineBA600 = parseInt(updatedItem.machineBA600);
      updatedItem.machineGraissage3 = parseInt(updatedItem.machineGraissage3);
      updatedItem.machineSoudage2 = parseInt(updatedItem.machineSoudage2);
      updatedItem.machineMarquage2 = parseInt(updatedItem.machineMarquage2);
      updatedItem.machineEmballage2 = parseInt(updatedItem.machineEmballage2);
      updatedItem.machineBA500 = parseInt(updatedItem.machineBA500);
      updatedItem.machineGraissage = parseInt(updatedItem.machineGraissage);
      updatedItem.machineSoudage1 = parseInt(updatedItem.machineSoudage1);
      updatedItem.machineMarquage1 = parseInt(updatedItem.machineMarquage1);
      updatedItem.machineEmballage1 = parseInt(updatedItem.machineEmballage1);

      const editedItem = await polyvalence.update(updatedItem);
      setPolyvalenceData(polyvalenceData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Polyvalence mise à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour de la polyvalence', 'error');
    }
  };

  const handleDelete = async (id) => {
    try {
      await polyvalence.delete(id);
      setPolyvalenceData(polyvalenceData.filter(item => item.id !== id));
      showNotification('Polyvalence supprimée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression de la polyvalence', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Handle navigation with unsaved changes
  const handleNavigation = (path) => {
    if (checkUnsavedChanges()) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      window.location.href = path;
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    discardChanges();
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Polyvalence
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Search input field */}
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-start' }}>
          <TextField
            placeholder="Rechercher par Opérateur..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: 'rgba(99, 102, 241, 0.6)', mr: 1 }} />,
            }}
            sx={{
              width: { xs: '100%', sm: '400px' },
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(15, 23, 42, 0.8)',
                '& fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'rgba(99, 102, 241, 0.8)',
                },
              },
              '& .MuiInputBase-input': {
                color: 'white',
                '&::placeholder': {
                  color: 'rgba(255, 255, 255, 0.7)',
                  opacity: 1,
                },
              },
            }}
          />
        </Box>

        <DataTable
          title=""
          columns={columns}
          data={polyvalenceData.filter(item => {
            if (!searchTerm.trim()) return true;
            const searchLower = searchTerm.toLowerCase();
            return item.operateur && item.operateur.toString().toLowerCase().includes(searchLower);
          })}
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSave={handleSave}
          loading={loading}
        />
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default Polyvalence;
