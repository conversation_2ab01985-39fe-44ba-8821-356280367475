import axios from 'axios';
import generateCustomPlanning from '../utils/generateCustomPlanning.js';

const API_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Data structure for localStorage (only for products)
const emptyDataStructure = {
  excelProducts: [],
  manualProducts: [],
};

// Initialize localStorage data
const initializeData = () => {
  try {
    const data = localStorage.getItem('planificationMichaudData');
    if (data) {
      return { ...emptyDataStructure, ...JSON.parse(data) };
    }
  } catch (error) {
    // Error parsing localStorage data - use empty structure
  }
  return { ...emptyDataStructure };
};

// Save data to localStorage
const saveToLocalStorage = (data) => {
  try {
    localStorage.setItem('planificationMichaudData', JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// Initialize data
const localData = initializeData();

// Debug function to inspect localStorage (for development)
export const debugLocalStorage = () => {
  return localData;
};

// Generic CRUD operations for backend entities
const createCRUD = (endpoint) => ({
  getAll: async () => {
    try {
      const response = await api.get(`/${endpoint}`);
      return response.data;
    } catch (error) {
      return [];
    }
  },

  add: async (item) => {
    try {
      const response = await api.post(`/${endpoint}`, item);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  update: async (item) => {
    try {
      const response = await api.put(`/${endpoint}/${item.id}`, item);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  delete: async (id) => {
    try {
      const response = await api.delete(`/${endpoint}/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  replaceAll: async (data) => {
    try {
      const response = await api.put(`/${endpoint}/replace-all`, data);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
});

// Export CRUD operations for navbar tables (backend entities)
export const fluxProduits = createCRUD('flux-produits');
export const machines = createCRUD('machines');
export const transit = createCRUD('transit');
export const ressourcesHumaines = createCRUD('ressources-humaines');
export const polyvalence = createCRUD('polyvalence');
export const ressourcesMaterielles = createCRUD('ressources-materielles');
export const stockMP = createCRUD('stock-mp');

// Convenience functions for specific entities
export const getRessourcesMaterielles = () => ressourcesMaterielles.getAll();
export const addRessourceMaterielle = (item) => ressourcesMaterielles.add(item);
export const updateRessourceMaterielle = (item) => ressourcesMaterielles.update(item);
export const deleteRessourceMaterielle = (id) => ressourcesMaterielles.delete(id);

export const getStockMP = () => stockMP.getAll();

// Products API (localStorage only - no backend persistence)
export const getProduits = async (entryMethod = 'excel', clearData = false) => {
  const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';

  // Always get fresh data from localStorage
  const freshLocalData = initializeData();

  // Clear data if explicitly requested (e.g., when navigating from Home)
  if (clearData) {
    freshLocalData[productsKey] = [];
    // Always save to localStorage to persist the cleared state
    saveToLocalStorage(freshLocalData);
    return [];
  }

  // Return existing data (persist between page navigations)
  return freshLocalData[productsKey] || [];
};

export const addProduit = async (produit, entryMethod = 'excel') => {
  const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';

  // Always get fresh data from localStorage
  const freshLocalData = initializeData();

  if (!freshLocalData[productsKey]) {
    freshLocalData[productsKey] = [];
  }

  const newItem = { ...produit, id: produit.id || Date.now().toString() };
  freshLocalData[productsKey].push(newItem);

  // Save both Excel and manual products to localStorage for persistence
  saveToLocalStorage(freshLocalData);

  return newItem;
};

export const updateProduit = async (produit, entryMethod = 'excel') => {
  const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';

  // Always get fresh data from localStorage
  const freshLocalData = initializeData();

  if (!freshLocalData[productsKey]) {
    freshLocalData[productsKey] = [];
  }

  const index = freshLocalData[productsKey].findIndex(item => item.id === produit.id);
  if (index !== -1) {
    freshLocalData[productsKey][index] = produit;
  } else {
    freshLocalData[productsKey].push(produit);
  }

  // Save both Excel and manual products to localStorage for persistence
  saveToLocalStorage(freshLocalData);

  return produit;
};

export const deleteProduit = async (id, entryMethod = 'excel') => {
  const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';

  // Always get fresh data from localStorage
  const freshLocalData = initializeData();

  if (!freshLocalData[productsKey]) {
    return { message: 'Item deleted successfully' };
  }

  const index = freshLocalData[productsKey].findIndex(item => item.id === id);
  if (index !== -1) {
    freshLocalData[productsKey].splice(index, 1);
  }

  // Save both Excel and manual products to localStorage for persistence
  saveToLocalStorage(freshLocalData);

  return { message: 'Item deleted successfully' };
};

// Clear products data
export const clearProductsData = (entryMethod = 'excel') => {
  const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';

  // Always get fresh data from localStorage
  const freshLocalData = initializeData();
  freshLocalData[productsKey] = [];

  // Always save to localStorage to persist the cleared state
  saveToLocalStorage(freshLocalData);

  return { success: true, message: 'Products data cleared successfully' };
};

// Excel upload handler
export const uploadExcel = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload-excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Store Excel products in localStorage
    if (response.data && response.data.length > 0) {
      // Always get fresh data from localStorage before updating
      const freshLocalData = initializeData();
      freshLocalData.excelProducts = response.data;
      saveToLocalStorage(freshLocalData);
    }

    return response.data;
  } catch (error) {
    throw error;
  }
};

// Planning generation
export const generatePlanning = async (entryMethod = 'excel', isRamadanHours = false, schedulingAlgorithm = 'FIFO') => {
  try {
    // CRITICAL FIX: Always read fresh data from localStorage instead of using cached localData
    const freshLocalData = initializeData();

    const productsKey = entryMethod === 'excel' ? 'excelProducts' : 'manualProducts';
    const produits = freshLocalData[productsKey] || [];

    if (produits.length === 0) {
      console.warn('No products available for planning generation');
      return [];
    }

    // Get navbar table data from backend
    const [fluxProduitsData, machinesData, polyvalenceData, ressourcesHumainesData, transitData] = await Promise.all([
      fluxProduits.getAll(),
      machines.getAll(),
      polyvalence.getAll(),
      ressourcesHumaines.getAll(),
      transit.getAll()
    ]);

    // Generate planning using the custom planning function with working hours mode and scheduling algorithm
    const planningData = generateCustomPlanning({
      produits,
      fluxProduits: fluxProduitsData,
      machines: machinesData,
      polyvalence: polyvalenceData,
      ressourcesHumaines: ressourcesHumainesData,
      transit: transitData,
      isRamadanHours,
      schedulingAlgorithm
    });

    return planningData;
  } catch (error) {
    // Re-throw deadline errors to show user the message
    if (error.message && error.message.includes('Veuillez prolonger le délai')) {
      throw error;
    }

    // For other errors, return empty array
    return [];
  }
};

// Planning de Matérielles generation
export const generatePlanningMaterielles = async () => {
  try {
    // Get required data from backend
    const [ressourcesMaterielles, stockMP] = await Promise.all([
      getRessourcesMaterielles(),
      getStockMP()
    ]);

    // Generate planning matérielles based on resources and stock
    const planningMaterielles = ressourcesMaterielles.map(resource => {
      const stock = stockMP.find(s => s.codeProduit === resource.codeProduit && s.composant === resource.composant);
      const quantiteStock = stock ? stock.quantiteEnStock || 0 : 0;
      const seuil = resource.seuil || 0;
      const moq = resource.moq || 0;

      // Calculate quantity to order
      let qteACommander = 0;
      if (quantiteStock < seuil) {
        qteACommander = Math.max(moq, seuil - quantiteStock);
      }

      return {
        ...resource,
        quantiteEnStock: quantiteStock,
        qteACommander
      };
    });

    return planningMaterielles;
  } catch (error) {
    return [];
  }
};
