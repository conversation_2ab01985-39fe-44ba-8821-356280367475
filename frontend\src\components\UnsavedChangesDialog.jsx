import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button
} from '@mui/material';

const UnsavedChangesDialog = ({ open, onCancel, onConfirm }) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="unsaved-changes-dialog-title"
      aria-describedby="unsaved-changes-dialog-description"
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: 'rgba(15, 23, 42, 0.95)',
          backdropFilter: 'blur(8px)',
          borderRadius: 2,
          border: '1px solid rgba(99, 102, 241, 0.1)',
          margin: { xs: 1, sm: 2 },
          maxHeight: { xs: '90vh', sm: '80vh' },
          width: { xs: 'calc(100% - 16px)', sm: 'auto' },
          minWidth: { xs: '300px', sm: '400px' },
        }
      }}
    >
      <DialogTitle id="unsaved-changes-dialog-title">
        Changements non sauvegardés
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="unsaved-changes-dialog-description">
          Vous avez des changements non sauvegardés. Voulez-vous vraiment quitter cette page sans sauvegarder?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onCancel} color="primary">
          Annuler
        </Button>
        <Button onClick={onConfirm} color="error" autoFocus>
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UnsavedChangesDialog;
